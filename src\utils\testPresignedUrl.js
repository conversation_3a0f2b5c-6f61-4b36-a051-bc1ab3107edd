// src/utils/testPresignedUrl.js
// Utility to test and diagnose presigned URL issues

import { GenerationService } from '../services/generationService';
import { nhost } from '../services/nhost';

/**
 * Test presigned URL functionality for a specific workflow file
 * This can be called from the browser console to diagnose issues
 */
export async function testWorkflowPresignedUrl(workflowFileId) {
    console.log(`🧪 Testing presigned URL for workflow: ${workflowFileId}`);
    
    try {
        // Run the diagnostic function
        const diagnostics = await GenerationService.diagnosePresignedUrlIssue(workflowFileId);
        
        console.log('📋 Diagnostic Results:', diagnostics);
        
        // Provide recommendations based on results
        if (diagnostics.overallStatus === 'healthy') {
            console.log('✅ All checks passed! Presigned URL should work.');
        } else {
            console.log('❌ Issues detected:');
            
            if (!diagnostics.userSession?.sessionValid) {
                console.log('  - User session is invalid. Please log in again.');
            }
            
            if (diagnostics.fileExists?.error || !diagnostics.fileExists) {
                console.log('  - Workflow file does not exist or cannot be accessed.');
                console.log('  - Check if the workflow file ID is correct.');
                console.log('  - Verify the file exists in the sauces bucket.');
            }
            
            if (diagnostics.storageAccess?.error) {
                console.log('  - Storage access issue:', diagnostics.storageAccess.error);
                console.log('  - Check Hasura permissions for storage.files table.');
            }
            
            if (diagnostics.presignedUrlAttempt?.error) {
                console.log('  - Presigned URL generation failed:', diagnostics.presignedUrlAttempt.error);
                console.log('  - This might be a bucket permission or Nhost configuration issue.');
            }
        }
        
        return diagnostics;
        
    } catch (error) {
        console.error('💥 Test failed:', error);
        return { error: error.message };
    }
}

/**
 * Test presigned URL for a known workflow file ID
 * Replace with actual workflow file ID from your database
 */
export async function testKnownWorkflow() {
    // First, let's find a workflow file ID from the tools table
    console.log('🔍 Looking for workflow files...');
    
    try {
        const { data, error } = await nhost.graphql.request(`
            query FindWorkflowFiles {
                tools(where: { workflow_file_id: { _is_null: false } }, limit: 5) {
                    id
                    name
                    workflow_file_id
                }
            }
        `);
        
        if (error) {
            console.error('❌ Failed to find workflow files:', error);
            return;
        }
        
        const tools = data?.tools || [];
        if (tools.length === 0) {
            console.log('⚠️ No tools with workflow files found');
            return;
        }
        
        console.log('📁 Found tools with workflows:', tools);
        
        // Test the first one
        const firstTool = tools[0];
        console.log(`🧪 Testing workflow for tool: ${firstTool.name} (${firstTool.id})`);
        
        return await testWorkflowPresignedUrl(firstTool.workflow_file_id);
        
    } catch (error) {
        console.error('💥 Failed to find workflow files:', error);
        return { error: error.message };
    }
}

/**
 * Test bucket access and permissions
 */
export async function testBucketAccess() {
    console.log('🪣 Testing bucket access...');
    
    try {
        const bucketValidation = await GenerationService.validateBucketConfiguration();
        
        console.log('📋 Bucket validation results:', bucketValidation);
        
        // Provide specific feedback
        Object.entries(bucketValidation).forEach(([bucketName, result]) => {
            if (result.valid) {
                console.log(`✅ ${bucketName} bucket: OK`);
            } else {
                console.log(`❌ ${bucketName} bucket: ${result.error || 'Failed'}`);
            }
        });
        
        return bucketValidation;
        
    } catch (error) {
        console.error('💥 Bucket test failed:', error);
        return { error: error.message };
    }
}

/**
 * Run all tests
 */
export async function runAllTests() {
    console.log('🚀 Running comprehensive presigned URL tests...');
    
    const results = {
        bucketAccess: null,
        workflowTest: null,
        timestamp: new Date().toISOString()
    };
    
    try {
        // Test bucket access
        console.log('\n1️⃣ Testing bucket access...');
        results.bucketAccess = await testBucketAccess();
        
        // Test workflow presigned URL
        console.log('\n2️⃣ Testing workflow presigned URL...');
        results.workflowTest = await testKnownWorkflow();
        
        console.log('\n📊 Complete test results:', results);
        
        return results;
        
    } catch (error) {
        console.error('💥 Test suite failed:', error);
        results.error = error.message;
        return results;
    }
}

// Make functions available globally for console testing
if (typeof window !== 'undefined') {
    window.testPresignedUrl = {
        testWorkflowPresignedUrl,
        testKnownWorkflow,
        testBucketAccess,
        runAllTests
    };
    
    console.log('🔧 Presigned URL test utilities loaded. Available functions:');
    console.log('  - window.testPresignedUrl.runAllTests()');
    console.log('  - window.testPresignedUrl.testKnownWorkflow()');
    console.log('  - window.testPresignedUrl.testBucketAccess()');
    console.log('  - window.testPresignedUrl.testWorkflowPresignedUrl(fileId)');
}
