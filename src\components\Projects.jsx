// src/components/Projects.jsx
import React, { useState, useEffect, useCallback } from 'react';
import {
    Grid, CardContent, CardActions, CircularProgress, CardMedia, Paper, Chip, Tooltip, Link as MuiLink,
    Dialog, DialogTitle, DialogContent, DialogActions, IconButton
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import CreditScoreIcon from '@mui/icons-material/CreditScore';
import VisibilityIcon from '@mui/icons-material/Visibility';
import DeleteIcon from '@mui/icons-material/Delete';
import SaveIcon from '@mui/icons-material/Save'; // For saving local projects
import ImageSearchIcon from '@mui/icons-material/ImageSearch';

// Design System Components
import {
    Stack,
    Heading,
    Text,
    Button,
    Card,
    Alert
} from './design-system/index';
import { Carousel } from 'react-responsive-carousel'; // If using a carousel for multiple outputs
import 'react-responsive-carousel/lib/styles/carousel.min.css'; // Carousel styles

import { useUserId } from '@nhost/react';
import { gql, useQuery, useMutation } from '@apollo/client';
import { nhost } from '../services/nhost'; // For user ID, not direct GQL

// Services (if needed for saving local projects from here, though Dashboard might handle it)
import { GenerationService } from '../services/generationService';
import { CreditService } from '../services/creditService';


// --- REFACTORED GraphQL Query for `projects` table ---
const GET_USER_SAVED_PROJECTS_QUERY = gql`
  query GetUserSavedProjects($userId: uuid!) {
    projects(
      where: { user_id: { _eq: $userId } },
      order_by: { saved_at: desc }
    ) {
      id
      user_id
      prompt_id
      name
      input_file_url
      prompt_input_text
      credit_cost
      generation_duration_ms
      outputs
      created_at
      updated_at
      saved_at

      # Optional: Link to prompt table to get tool_id if not storing on projects
      prompt {
          id
          tool_id
          input_file
          user_prompt
          server_type
          status
      }
    }
  }
`;

const DELETE_PROJECT_MUTATION = gql` # (No change if only deleting by ID)
  mutation DeleteProject($projectId: uuid!) {
    delete_projects_by_pk(id: $projectId) { id }
  }
`;

// Simple Output Viewer Dialog
function OutputViewerDialog({ open, onClose, outputs }) {
    if (!outputs || outputs.length === 0) return null;
    return (
        <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
            <DialogTitle>View Outputs</DialogTitle>
            <DialogContent>
                {outputs.length === 1 ? (
                    <img src={outputs[0]} alt="Generated Output" style={{ width: '100%', height: 'auto', borderRadius: '4px' }} />
                ) : (
                    <Carousel showThumbs={false} showStatus={false} useKeyboardArrows>
                        {outputs.map((url, index) => (
                            <div key={index}>
                                <img src={url} alt={`Output ${index + 1}`} style={{ maxHeight: '70vh', objectFit: 'contain' }}/>
                            </div>
                        ))}
                    </Carousel>
                )}
            </DialogContent>
            <DialogActions>
                <Button onClick={onClose}>Close</Button>
            </DialogActions>
        </Dialog>
    );
}


function Projects({ showUnsaved = false, onSaveLocalProject, onActionFeedback }) { // Added props
    const theme = useTheme();
    const userId = useUserId();
    const [localProjects, setLocalProjects] = useState([]);
    const [selectedOutputs, setSelectedOutputs] = useState([]);
    const [viewerOpen, setViewerOpen] = useState(false);

    // Fetch saved projects
    const { loading: loadingSaved, error: errorSaved, data: dataSaved, refetch: refetchSaved } = useQuery(
        GET_USER_SAVED_PROJECTS_QUERY,
        { variables: { userId: userId }, skip: !userId, fetchPolicy: 'cache-and-network' }
    );

    // Delete mutation
    const [deleteProjectMutation, { loading: loadingDelete }] = useMutation(DELETE_PROJECT_MUTATION, {
        refetchQueries: [{ query: GET_USER_SAVED_PROJECTS_QUERY, variables: { userId } }],
        onError: (err) => {
            console.error("Error deleting project:", err);
            if (onActionFeedback) onActionFeedback(`Failed to delete project: ${err.message}`, 'error');
        },
        onCompleted: () => {
            if (onActionFeedback) onActionFeedback('Project deleted successfully.', 'success');
        }
    });

    // Load local (unsaved) projects if showUnsaved is true
    useEffect(() => {
        if (!userId || !showUnsaved) {
            setLocalProjects([]); // Clear if not showing or no user
            return;
        }

        const loadLocal = () => {
            const local = [];
            const savedProjectPromptIds = dataSaved?.projects?.map(p => p.prompt_id).filter(Boolean) || [];
            try {
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    // Assuming local items are stored with a key like 'unsaved_gen_{promptLogId}'
                    // Or 'output-{promptId}' as per previous versions
                    if (key && (key.startsWith('unsaved_gen_') || key.startsWith('output-'))) {
                        const itemData = JSON.parse(localStorage.getItem(key));
                        // Ensure it's not already saved
                        if (itemData && itemData.promptId && !savedProjectPromptIds.includes(itemData.promptId)) {
                            // Map to a structure similar to saved projects for consistent display
                            local.push({
                                id: key, // Use localStorage key as temp ID
                                isLocal: true,
                                prompt_id: itemData.promptId,
                                input_file_url: itemData.nhostInputFile?.nhostFileUrl || itemData.inputFileUrl || null, // from runGenerationJob result
                                prompt_input_text: itemData.userPromptText || itemData.promptUsed || "N/A",
                                created_at: itemData.timestamp || new Date().toISOString(), // Timestamp of generation
                                credit_cost: itemData.usageDetails?.calculatedCost ?? itemData.credit_cost ?? 0,
                                generation_duration_ms: itemData.usageDetails?.durationMs ?? itemData.generation_duration_ms ?? 0,
                                outputs: itemData.generationOutput?.outputs || itemData.outputs || (itemData.outputUrl ? [itemData.outputUrl] : []),
                                // Include all data needed for saving
                                fullLocalData: itemData // Store the original local data for saving
                            });
                        } else if (itemData && itemData.promptId && savedProjectPromptIds.includes(itemData.promptId)) {
                            localStorage.removeItem(key); // Clean up already saved
                        }
                    }
                }
                local.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
                setLocalProjects(local);
            } catch (err) {
                console.error("[Projects] Error loading local projects:", err);
                if (onActionFeedback) onActionFeedback("Could not load unsaved projects.", "error");
            }
        };
        loadLocal();
    }, [userId, showUnsaved, dataSaved, onActionFeedback]);


    const handleDelete = (projectId, isLocalItem) => {
        if (isLocalItem) {
            try {
                localStorage.removeItem(projectId); // projectId here is the localStorage key
                setLocalProjects(prev => prev.filter(p => p.id !== projectId));
                if (onActionFeedback) onActionFeedback('Unsaved generation removed.', 'info');
            } catch (e) {
                if (onActionFeedback) onActionFeedback('Failed to remove unsaved item.', 'error');
            }
        } else {
            if (window.confirm('Are you sure you want to delete this saved project?')) {
                deleteProjectMutation({ variables: { projectId } });
            }
        }
    };

    const handleViewOutputs = (outputsArray) => {
        setSelectedOutputs(outputsArray || []);
        setViewerOpen(true);
    };

    const renderProjectItem = (project, isLocalItem = false) => {
        const displayOutputs = project.outputs || [];
        const previewUrl = displayOutputs.length > 0 ? displayOutputs[0] : project.input_file_url; // Fallback to input
        const isVideo = previewUrl && /\.(mp4|mov|avi|webm)$/i.test(previewUrl);

        return (
            <Card
                variant={isLocalItem ? "outlined" : "default"}
                sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    height: '100%',
                    border: isLocalItem ? `1px dashed ${theme.palette.warning.main}` : undefined
                }}
            >
                <CardMedia
                    component={isVideo ? "video" : "img"}
                    height="160"
                    image={previewUrl || "https://via.placeholder.com/345x160.png?text=No+Preview"}
                    alt={project.prompt_input_text || "Project Preview"}
                    sx={{ objectFit: 'cover', backgroundColor: isVideo ? '#000' : 'action.hover' }}
                    controls={isVideo}
                />
                <CardContent sx={{ flexGrow: 1, p: 1.5 }}>
                    <Stack direction="row" justifyContent="space-between" alignItems="flex-start" spacing={1} sx={{ mb: 1 }}>
                        <Tooltip title={project.prompt_input_text || "No prompt"}>
                            <Heading
                                level="h3"
                                size="base"
                                weight={600}
                                sx={{
                                    overflow: 'hidden',
                                    textOverflow: 'ellipsis',
                                    whiteSpace: 'nowrap',
                                    flex: 1
                                }}
                            >
                                {project.prompt_input_text || project.name || (project.prompt?.tool_id || "Project")}
                            </Heading>
                        </Tooltip>
                        {isLocalItem && (
                            <Chip
                                label="Unsaved"
                                size="small"
                                color="warning"
                                variant="outlined"
                                sx={{ height: '20px', fontSize: '0.7rem' }}
                            />
                        )}
                        {!isLocalItem && project.saved_at && (
                            <Chip
                                label="Saved"
                                size="small"
                                color="success"
                                variant="outlined"
                                sx={{ height: '20px', fontSize: '0.7rem' }}
                            />
                        )}
                    </Stack>
                    <Text size="xs" variant="muted" sx={{ display: 'block', mb: 0.5 }}>
                        {new Date(project.saved_at || project.created_at).toLocaleString()}
                    </Text>
                    <Text size="xs" variant="muted" sx={{ display: 'block', mb: 1 }}>
                        Tool: {project.prompt?.tool_id || "N/A"}
                    </Text>
                    {!isLocalItem && (
                        <Stack direction="row" justifyContent="space-between" spacing={1}>
                            <Stack direction="row" alignItems="center" spacing={0.5}>
                                <AccessTimeIcon sx={{ fontSize: '0.75rem' }} />
                                <Text size="xs" variant="muted">
                                    {project.generation_duration_ms ? `${(project.generation_duration_ms / 1000).toFixed(1)}s` : '-'}
                                </Text>
                            </Stack>
                            <Stack direction="row" alignItems="center" spacing={0.5}>
                                <CreditScoreIcon sx={{ fontSize: '0.75rem' }} />
                                <Text size="xs" variant="muted">
                                    {project.credit_cost ?? '-'}cr
                                </Text>
                            </Stack>
                        </Stack>
                    )}
                </CardContent>
                <CardActions sx={{ justifyContent: 'space-between', px: 1.5, py: 1, borderTop: `1px solid ${theme.palette.divider}` }}>
                    {displayOutputs.length > 0 && (
                        <Button
                            size="small"
                            variant="text"
                            startIcon={<VisibilityIcon />}
                            onClick={() => handleViewOutputs(displayOutputs)}
                        >
                            View ({displayOutputs.length})
                        </Button>
                    )}
                    {isLocalItem && onSaveLocalProject && (
                        <Button
                            size="small"
                            color="primary"
                            variant="contained"
                            startIcon={<SaveIcon />}
                            onClick={() => onSaveLocalProject(project.fullLocalData, project.id)}
                        >
                            Save
                        </Button>
                    )}
                    <Button
                        size="small"
                        color="error"
                        variant="text"
                        startIcon={<DeleteIcon />}
                        onClick={() => handleDelete(project.id, isLocalItem)}
                    >
                        Delete
                    </Button>
                </CardActions>
            </Card>
        );
    };

    const displayedProjects = showUnsaved ? localProjects : (dataSaved?.projects || []);
    const isLoading = showUnsaved ? false : loadingSaved; // Loading only applies to saved if not showing unsaved
    const displayError = showUnsaved ? null : errorSaved;

    if (isLoading) return (
        <Stack justifyContent="center" alignItems="center" sx={{ p: 3 }}>
            <CircularProgress />
        </Stack>
    );
    if (displayError) return (
        <Alert severity="error" sx={{ m: 2 }}>
            Error loading projects: {displayError.message}
        </Alert>
    );

    return (
        <>
            <Grid container spacing={{ xs: 2, md: 3 }}>
                {displayedProjects.map(project => (
                    <Grid item xs={12} sm={6} md={4} lg={3} key={project.id}>
                        {renderProjectItem(project, project.isLocal)}
                    </Grid>
                ))}
            </Grid>
            {displayedProjects.length === 0 && (
                <Stack alignItems="center" sx={{ textAlign: 'center', mt: 4 }}>
                    <Text variant="muted">
                        No {showUnsaved ? "unsaved generations" : "saved projects"} found.
                    </Text>
                </Stack>
            )}
            <OutputViewerDialog open={viewerOpen} onClose={() => setViewerOpen(false)} outputs={selectedOutputs} />
        </>
    );
}

export default Projects;