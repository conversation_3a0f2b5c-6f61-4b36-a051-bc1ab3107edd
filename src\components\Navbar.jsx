// src/components/Navbar.jsx
import React, { useState, useRef, useEffect } from 'react';
import { Box, Drawer, List, ListItem, ListItemButton, ListItemIcon, ListItemText, IconButton, FormControlLabel, Switch, useTheme, AppBar, Toolbar, CircularProgress } from '@mui/material';
import { useSignOut, useAuthenticationStatus } from '@nhost/react';
import { Link } from 'react-router-dom';

// Design System Components
import {
    Heading,
    Text
} from './design-system/index';

// --- Import Icons ---
import MenuIcon from '@mui/icons-material/Menu';
import DashboardIcon from '@mui/icons-material/Dashboard';
import BuildIcon from '@mui/icons-material/Build';
import HistoryIcon from '@mui/icons-material/History';
import AccountBalanceWalletIcon from '@mui/icons-material/AccountBalanceWallet';
import SettingsIcon from '@mui/icons-material/Settings';
import LogoutIcon from '@mui/icons-material/Logout';
import LoginIcon from '@mui/icons-material/Login';
import PersonAddIcon from '@mui/icons-material/PersonAdd';
// --- End Import Icons ---

const drawerWidth = 250;

// Accept mainContentId as a prop
function Navbar({ darkMode, handleToggle, mainContentId }) {
  const { isAuthenticated, isLoading } = useAuthenticationStatus();
  const { signOut, isSigningOut } = useSignOut();
  // const navigate = useNavigate(); // Commented out as not currently used
  const theme = useTheme();
  const [mobileOpen, setMobileOpen] = useState(false);
  const menuButtonRef = useRef(null);
  // Store previous open state to detect closing
  const prevMobileOpen = useRef(mobileOpen);

  // Effect to return focus when drawer closes
  useEffect(() => {
    // Check if the drawer *was* open and *is now* closed
    if (prevMobileOpen.current && !mobileOpen) {
      // Try focusing the main content area first
      const mainContent = document.getElementById(mainContentId);
      if (mainContent) {
        console.log(`[Navbar] Drawer closed, focusing main content (#${mainContentId})`);
        // Use timeout to ensure drawer animation finishes and element is focusable
        const timer = setTimeout(() => {
            mainContent.focus();
            // Optional: Fallback to menu button if main content focus fails?
            // if (document.activeElement !== mainContent && menuButtonRef.current) {
            //    menuButtonRef.current.focus();
            // }
        }, 150); // Adjust delay if needed
        return () => clearTimeout(timer);
      } else if (menuButtonRef.current) {
          // Fallback: If main content not found, focus the menu button
          console.log('[Navbar] Drawer closed, main content not found, focusing menu button.');
          const timer = setTimeout(() => {
              menuButtonRef.current?.focus();
          }, 150);
         return () => clearTimeout(timer);
      }
    }
    // Update previous state for next render
    prevMobileOpen.current = mobileOpen;
  // Add mainContentId to dependency array
  }, [mobileOpen, mainContentId]);

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  // This function will now also trigger the useEffect above when mobileOpen becomes false
  const handleDrawerItemClick = () => {
      handleDrawerToggle(); // Close the drawer
  };


  const handleLogout = async () => {
    handleDrawerItemClick(); // Close drawer before logout action
    try {
      await signOut();
    } catch (error) {
      console.error("Nhost sign out error:", error);
    }
  };

  // --- Drawer Content ---
  const drawerContent = (
    <Box sx={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
      <Box sx={{ p: 2, borderBottom: `1px solid ${theme.palette.divider}` }}>
        <Heading level="h2" size="lg" weight={700} sx={{ textAlign: 'center' }}>
          AiGenius
        </Heading>
      </Box>
      {/* Update ListItems to call handleDrawerItemClick */}
      <List sx={{ flexGrow: 1, overflowY: 'auto' }}>
        <ListItem disablePadding>
          <ListItemButton component={Link} to="/dashboard" aria-label="Dashboard" onClick={handleDrawerItemClick}>
            <ListItemIcon><DashboardIcon sx={{ color: 'text.secondary' }} /></ListItemIcon>
            <ListItemText primary="Dashboard" />
          </ListItemButton>
        </ListItem>
        <ListItem disablePadding>
          <ListItemButton component={Link} to="/tools-tutorials" aria-label="Tools & Tutorials" onClick={handleDrawerItemClick}>
            <ListItemIcon><BuildIcon sx={{ color: 'text.secondary' }} /></ListItemIcon>
            <ListItemText primary="Tools & Tutorials" />
          </ListItemButton>
        </ListItem>
        {isAuthenticated && (
          <>
            <ListItem disablePadding>
              <ListItemButton component={Link} to="/history" aria-label="History" onClick={handleDrawerItemClick}>
                <ListItemIcon><HistoryIcon sx={{ color: 'text.secondary' }} /></ListItemIcon>
                <ListItemText primary="History" />
              </ListItemButton>
            </ListItem>
            <ListItem disablePadding>
              <ListItemButton component={Link} to="/my-plan" aria-label="My Plan" onClick={handleDrawerItemClick}>
                <ListItemIcon><AccountBalanceWalletIcon sx={{ color: 'text.secondary' }} /></ListItemIcon>
                <ListItemText primary="My Plan" />
              </ListItemButton>
            </ListItem>
            <ListItem disablePadding>
              <ListItemButton component={Link} to="/profile-settings" aria-label="Profile Settings" onClick={handleDrawerItemClick}>
                <ListItemIcon><SettingsIcon sx={{ color: 'text.secondary' }} /></ListItemIcon>
                <ListItemText primary="Settings" />
              </ListItemButton>
            </ListItem>
          </>
        )}
      </List>
      <Box sx={{ p: 2, borderTop: `1px solid ${theme.palette.divider}` }}>
        <FormControlLabel
          control={<Switch checked={darkMode} onChange={handleToggle} color="secondary" size="small" />}
          label={<Text size="sm">Dark Mode</Text>}
          sx={{ display: 'flex', justifyContent: 'space-between', ml: 0, mb: 1 }}
        />
        {isLoading ? ( <Box sx={{ display: 'flex', justifyContent: 'center', p: 1 }}><CircularProgress size={24} /></Box> )
        : isAuthenticated ? (
          <ListItem disablePadding>
            {/* Use handleLogout which now closes drawer first */}
            <ListItemButton onClick={handleLogout} disabled={isSigningOut} aria-label="Logout">
              <ListItemIcon><LogoutIcon sx={{ color: 'text.secondary' }} /></ListItemIcon>
              <ListItemText primary={isSigningOut ? 'Logging out...' : 'Logout'} />
              {isSigningOut && <CircularProgress size={20} sx={{ ml: 1 }} />}
            </ListItemButton>
          </ListItem>
        ) : (
          <>
            <ListItem disablePadding>
              <ListItemButton component={Link} to="/login" aria-label="Login" onClick={handleDrawerItemClick}>
                <ListItemIcon><LoginIcon sx={{ color: 'text.secondary' }} /></ListItemIcon>
                <ListItemText primary="Login" />
              </ListItemButton>
            </ListItem>
            <ListItem disablePadding>
              <ListItemButton component={Link} to="/register" aria-label="Register" onClick={handleDrawerItemClick}>
                <ListItemIcon><PersonAddIcon sx={{ color: 'text.secondary' }} /></ListItemIcon>
                <ListItemText primary="Register" />
              </ListItemButton>
            </ListItem>
          </>
        )}
      </Box>
    </Box>
  );
  // --- End Drawer Content ---


  return (
    <>
      {/* Mobile AppBar */}
      <AppBar
        position="fixed"
        sx={{
          display: { sm: 'none' },
          backgroundColor: theme.palette.background.paper,
          color: theme.palette.text.primary,
          zIndex: theme.zIndex.drawer + 1,
        }}
        elevation={1}
      >
        <Toolbar>
          <IconButton
            color="inherit" aria-label="Open drawer" edge="start"
            onClick={handleDrawerToggle} sx={{ mr: 2 }} ref={menuButtonRef}
          >
            <MenuIcon />
          </IconButton>
          <Heading level="h1" size="lg" weight={700} sx={{ flexGrow: 1 }}>
            AiGenius
          </Heading>
        </Toolbar>
      </AppBar>

      {/* Drawer Logic */}
      <Box
        component="nav"
        sx={{ width: { sm: drawerWidth }, flexShrink: { sm: 0 } }}
        aria-label="Main navigation"
      >
        {/* Temporary Drawer for Mobile */}
        <Drawer
          variant="temporary"
          open={mobileOpen}
          onClose={handleDrawerToggle} // Keep onClose for clicking outside
          ModalProps={{
              // Removed keepMounted based on previous attempt
          }}
          sx={{
            display: { xs: 'block', sm: 'none' },
            '& .MuiDrawer-paper': { boxSizing: 'border-box', width: drawerWidth },
          }}
        >
          {drawerContent}
        </Drawer>

        {/* Permanent Drawer for Desktop */}
        <Drawer
          variant="permanent"
          sx={{
            display: { xs: 'none', sm: 'block' },
            '& .MuiDrawer-paper': { boxSizing: 'border-box', width: drawerWidth, borderRight: `1px solid ${theme.palette.divider}` },
          }}
          open
        >
          {drawerContent}
        </Drawer>
      </Box>
    </>
  );
}

export default Navbar;
