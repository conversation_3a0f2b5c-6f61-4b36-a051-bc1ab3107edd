// src/App.jsx
import React from 'react';
import { Routes, Route, Navigate, useLocation, Link as RouterLink } from 'react-router-dom';
// Import Nhost hooks and client
import { useAuthenticationStatus } from '@nhost/react';
import { nhost } from './services/nhost';
import { refreshAuthToken } from './services/auth';

import { Box, CircularProgress, Typography, Button } from '@mui/material';
import Navbar from './components/Navbar';
import ProtectedRoute, { PublicOnlyRoute } from './components/ProtectedRoute';

// --- Debug Utilities (Development Only) ---
if (import.meta.env.DEV) {
  import('./utils/authDebug.js');
}

// --- Page Imports ---
import Login from './pages/Login';
import Register from './pages/Register';
import ForgotPassword from './pages/ForgotPassword';
import VerifyEmail from './pages/VerifyEmail';
import Dashboard from './pages/Dashboard';
import ReImagine from './pages/ReImagine';
import Img2Video from './pages/Img2Video';
import History from './pages/History';
import MyPlan from './pages/MyPlan';
import ProfileSettings from './pages/ProfileSettings';
import ToolsAndTutorials from './pages/ToolsAndTutorials';

/**
 * Validates the current token status and refreshes if needed
 * @param {boolean} force - Force token refresh even if session exists
 * @returns {Promise<object|null>} The current session or null if not authenticated
 */
const validateTokenStatus = async (force = false) => {
  // Skip validation during initial page load chunks to prevent unnecessary requests
  if (typeof window !== 'undefined' && document.readyState !== 'complete') {
    console.debug('[App] Skipping token validation during initial page load');
    return null;
  }

  try {
    // Check if we have a valid session
    const session = nhost.auth.getSession();

    // If we have a session with an access token and not forcing refresh, check if it's valid
    if (!force && session?.accessToken) {
      // Check if token is about to expire (within 5 minutes)
      const expiresIn = session.accessTokenExpiresIn;
      const now = Date.now();

      if (expiresIn && expiresIn > now + 5 * 60 * 1000) {
        // Token is still valid, no need to refresh
        console.debug('[App] Token is still valid, no refresh needed');

        // Log current token status
        console.debug('[App] Token Status:', {
          hasSession: true,
          expiresAt: new Date(session.accessTokenExpiresIn).toLocaleTimeString(),
          timeRemaining: Math.floor((session.accessTokenExpiresIn - now) / 1000) + ' seconds'
        });

        return session;
      }

      console.debug('[App] Token is expiring soon, refreshing');
    } else if (!session?.accessToken) {
      console.debug('[App] No valid session, attempting to refresh token');
    }

    // Refresh the token
    const refreshResult = await refreshAuthToken(force);

    if (!refreshResult.success) {
      console.error('[App] Token refresh failed:', refreshResult.error);
      return null;
    }

    console.debug('[App] Token refresh successful');
    return refreshResult.session;
  } catch (error) {
    console.error('[App] Error during token validation:', error);
    return null;
  }
};

const drawerWidth = 250;

// --- Main App Component ---
function App({ darkMode, handleToggle }) {
  const { isLoading: isLoadingAuth } = useAuthenticationStatus();

  // Track if initial validation has been performed
  const [initialValidationDone, setInitialValidationDone] = React.useState(false);

  // Handle initial token validation
  React.useEffect(() => {
    // Skip if still loading auth state or validation already done
    if (isLoadingAuth || initialValidationDone) return;

    const performInitialValidation = async () => {
      try {
        console.debug('[App] Performing initial token validation');
        await validateTokenStatus(false);
        setInitialValidationDone(true);
      } catch (err) {
        console.error('[App] Error during initial token validation:', err);
        setInitialValidationDone(true); // Mark as done even on error
      }
    };

    // Wait for page to be fully loaded
    if (document.readyState === 'complete') {
      performInitialValidation();
    } else {
      // Set up one-time load event listener
      const handleLoad = () => {
        window.removeEventListener('load', handleLoad);
        performInitialValidation();
      };
      window.addEventListener('load', handleLoad);

      // Cleanup
      return () => {
        window.removeEventListener('load', handleLoad);
      };
    }
  }, [isLoadingAuth, initialValidationDone]);

  // Handle localStorage changes (for multi-tab support)
  React.useEffect(() => {
    const handleStorageChange = async (event) => {
      // Only react to auth-related storage changes
      if (event.key && (
        event.key.includes('nhost') ||
        event.key.includes('auth') ||
        event.key === 'logout'
      )) {
        console.debug('[App] Storage change detected for:', event.key);
        try {
          await validateTokenStatus(true); // Force validation on storage change
        } catch (err) {
          console.error('[App] Error handling storage change:', err);
        }
      }
    };

    // Add storage event listener
    window.addEventListener('storage', handleStorageChange);

    // Cleanup function
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []);

  if (isLoadingAuth) {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
          bgcolor: 'background.default'
        }}
      >
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ display: 'flex', minHeight: '100vh', bgcolor: 'background.default' }}>
      {/* Pass the ID of the main content area to Navbar */}
      <Navbar darkMode={darkMode} handleToggle={handleToggle} mainContentId="main-content-area" />

      <Box
        component="main"
        id="main-content-area"
        tabIndex={-1}
        sx={{
          flexGrow: 1,
          py: { xs: 2, sm: 3, md: 4 },
          px: { xs: 2, sm: 3, md: 4 },
          width: { md: `calc(100% - ${drawerWidth}px)` },
          mt: { xs: '56px', sm: '64px' },
          overflowX: 'hidden',
          overflowY: 'auto',
          '&:focus': {
            outline: 'none',
          },
        }}
      >
        <Routes>
          {/* Public Routes */}
          <Route path="/login" element={<PublicOnlyRoute><Login /></PublicOnlyRoute>} />
          <Route path="/register" element={<PublicOnlyRoute><Register /></PublicOnlyRoute>} />
          <Route path="/forgot-password" element={<PublicOnlyRoute><ForgotPassword /></PublicOnlyRoute>} />
          {/* Verification Route */}
          <Route path="/verify-email" element={<ProtectedRoute><VerifyEmail /></ProtectedRoute>} />
          {/* Protected Routes */}
          <Route path="/dashboard" element={<ProtectedRoute><Dashboard /></ProtectedRoute>} />
          <Route path="/tools-tutorials" element={<ProtectedRoute><ToolsAndTutorials /></ProtectedRoute>} />
          <Route path="/tool/reimagine" element={<ProtectedRoute><ReImagine /></ProtectedRoute>} />
          <Route path="/tool/img2video" element={<ProtectedRoute><Img2Video /></ProtectedRoute>} />
          <Route path="/history" element={<ProtectedRoute><History /></ProtectedRoute>} />
          <Route path="/my-plan" element={<ProtectedRoute><MyPlan /></ProtectedRoute>} />
          <Route path="/profile-settings" element={<ProtectedRoute><ProfileSettings /></ProtectedRoute>} />
          {/* Default Route */}
          <Route path="/" element={<Navigate to="/dashboard" replace />} />
          {/* 404 Fallback */}
          <Route path="*" element={
            <Box sx={{ textAlign: 'center', mt: 5 }}>
              <Typography variant="h4">404 - Page Not Found</Typography>
              <Button component={RouterLink} to="/" sx={{ mt: 2 }}>Go Home</Button>
            </Box>
          } />
        </Routes>
      </Box>
    </Box>
  );
}

export default App;
