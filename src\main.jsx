// src/main.jsx
import React, { useState, useEffect, useMemo } from 'react';
import ReactDOM from 'react-dom/client';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import useMediaQuery from '@mui/material/useMediaQuery';
import getDesignTokens from './theme';
import App from './App';
import './index.css';

// Import Nhost provider, Apollo provider, and client
import { NhostProvider } from '@nhost/react';
import { NhostApolloProvider } from '@nhost/react-apollo';
import { nhost } from './services/nhost';
import { setupAuthErrorHandling } from './services/auth';

// Import test utilities for debugging (development only)
if (import.meta.env.DEV) {
  import('./utils/testPresignedUrl').then(module => {
    console.log('🔧 Development mode: Presigned URL test utilities loaded');
  }).catch(err => {
    console.warn('Failed to load test utilities:', err);
  });
}

/**
 * Main application component that sets up providers and theme
 */
function Main() {
  // Theme switching logic
  const prefersDarkMode = useMediaQuery('(prefers-color-scheme: dark)');
  const [darkMode, setDarkMode] = useState(prefersDarkMode);

  // Initialize auth error handling
  useEffect(() => {
    console.log('Setting up authentication error handling...');
    setupAuthErrorHandling();
  }, []);

  // Update dark mode when system preference changes
  useEffect(() => {
    setDarkMode(prefersDarkMode);
  }, [prefersDarkMode]);

  // Handle manual theme toggle
  const handleToggle = () => {
    setDarkMode(!darkMode);
    document.body.classList.toggle('light', darkMode);
    document.body.classList.toggle('dark', !darkMode);
  };

  // Create theme based on current mode
  const theme = useMemo(
    () => createTheme(getDesignTokens(darkMode ? 'dark' : 'light')),
    [darkMode],
  );

  // Apply theme classes to body
  useEffect(() => {
    if (darkMode) {
      document.body.classList.remove('light');
      document.body.classList.add('dark');
    } else {
      document.body.classList.add('light');
      document.body.classList.remove('dark');
    }
  }, [darkMode]);

  return (
    <React.StrictMode>
      {/* NhostProvider provides the core Nhost client and auth state */}
      <NhostProvider nhost={nhost}>
        {/* NhostApolloProvider sets up Apollo Client using Nhost config */}
        <NhostApolloProvider nhost={nhost}>
          <ThemeProvider theme={theme}>
            <CssBaseline />
            <BrowserRouter>
              <App darkMode={darkMode} handleToggle={handleToggle} />
            </BrowserRouter>
          </ThemeProvider>
        </NhostApolloProvider>
      </NhostProvider>
    </React.StrictMode>
  );
}

// Render the application
ReactDOM.createRoot(document.getElementById('root')).render(<Main />);
