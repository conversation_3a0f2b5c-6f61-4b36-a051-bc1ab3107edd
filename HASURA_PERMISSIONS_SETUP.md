# Hasura Permissions Setup Guide

This guide provides step-by-step instructions to fix the database schema mismatch error in the generation service.

## Error Description

The error occurs because <PERSON><PERSON> doesn't recognize the `user_id` field in the `prompts_insert_input` type:

```
field 'user_id' not found in type: 'prompts_insert_input'
```

## Root Cause

The issue is that the `user_id` field in the `prompts` table needs to be configured as a **column preset** in Hasura permissions, not as a regular insertable field. This is a security best practice that ensures users can only insert records with their own user ID.

## Solution Steps

### 1. Access Hasura Console

1. Make sure your Nhost local environment is running: `nhost dev`
2. Open the Hasura Console: `https://local.hasura.local.nhost.run`
3. Navigate to the **Data** tab

### 2. Configure Prompts Table Permissions

1. Go to **Data** > **prompts** table
2. Click on the **Permissions** tab
3. Find the **user** role (create it if it doesn't exist)

#### Insert Permissions:
1. Click **Edit** (pencil icon) for the **insert** permission
2. **Row insert permissions**: Select "Without any checks" (users can insert prompts)
3. **Column insert permissions**: 
   - ✅ Check: `tool_id`, `input_file`, `user_prompt`, `server_type`, `status`, `uploaded_file_id`
   - ❌ **DO NOT** check: `user_id` (this will be set automatically)
4. **Column presets**: 
   - Click "Add a preset"
   - Column: `user_id`
   - Value: `X-Nhost-User-Id`
5. Click **Save Permissions**

#### Select Permissions:
1. Click **Edit** for the **select** permission
2. **Row select permissions**: Choose "With custom check"
3. Condition: `{ "user_id": { "_eq": "X-Nhost-User-Id" } }`
4. **Column select permissions**: Check all columns users should read
5. Click **Save Permissions**

#### Update Permissions:
1. Click **Edit** for the **update** permission
2. **Row update permissions**: Choose "With custom check"
3. Condition: `{ "user_id": { "_eq": "X-Nhost-User-Id" } }`
4. **Column update permissions**: Check only fields users should modify (typically `status`, `error_message`)
5. Click **Save Permissions**

### 3. Verify Other Tables

Ensure similar permissions are set for other user-related tables:

#### Projects Table:
- Insert: Column preset `user_id` = `X-Nhost-User-Id`
- Select: Row condition `{ "user_id": { "_eq": "X-Nhost-User-Id" } }`
- Delete: Row condition `{ "user_id": { "_eq": "X-Nhost-User-Id" } }`

#### User_Wallet Table:
- Select: Row condition `{ "user_id": { "_eq": "X-Nhost-User-Id" } }`
- Update: Row condition `{ "user_id": { "_eq": "X-Nhost-User-Id" } }`

### 4. Apply Database Schema

If the tables don't exist yet, apply the database schema:

```bash
# Navigate to your project root
cd /path/to/your/project

# Apply the database schema
psql -h localhost -p 5432 -U postgres -d postgres -f aigenius_database_schema.sql
```

### 5. Test the Fix

After setting up permissions, test the generation service:

1. Try running a generation job
2. Check the console logs for successful prompt logging
3. Verify that `user_id` is automatically populated in the database

## Important Notes

- **Never** allow users to directly set their own `user_id` in insert operations
- Always use `X-Nhost-User-Id` column presets for user identification
- The `X-Nhost-User-Id` session variable is automatically provided by Nhost authentication
- Row-level security ensures users can only access their own data

## Troubleshooting

### If you still get permission errors:

1. **Check user authentication**: Ensure the user is properly logged in
2. **Verify session variables**: Check that `X-Nhost-User-Id` is present in the session
3. **Review table structure**: Ensure the `prompts` table exists with correct columns
4. **Check foreign keys**: Verify that referenced tables (tools, storage.files) exist

### Common mistakes:

- Including `user_id` in both insertable columns AND column presets
- Forgetting to set the column preset value to `X-Nhost-User-Id`
- Using incorrect session variable names (should be `X-Nhost-User-Id`, not `x-hasura-user-id`)

## Next Steps

After fixing the permissions:

1. Test the ReImagine tool functionality
2. Verify that prompt logs are created with correct user associations
3. Check that the generation service completes successfully
4. Monitor for any additional permission-related errors

For production deployment, ensure these same permissions are configured in your cloud Hasura instance.
